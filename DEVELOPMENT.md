# MatterhornMVP Development Guide

## Quick Start for Daily Development

### First Time Setup
1. Make sure <PERSON><PERSON> and <PERSON><PERSON> Compose are installed
2. <PERSON>lone the repository
3. Make the development scripts executable:
   ```bash
   chmod +x dev-start.sh dev-stop.sh dev-rebuild.sh
   ```

### Daily Development Workflow

#### Starting Development Environment
```bash
./dev-start.sh
```
This will:
- Start all services without rebuilding (much faster!)
- Use environment variables from `.env` file
- Enable development mode with volume mounts for hot reloading

#### Making Code Changes
- **Frontend changes**: The Angular dev server will automatically reload
- **Backend changes**: Run `./dev-rebuild.sh` and select the specific service
- **Database changes**: Restart the db service if needed

#### Stopping Development Environment
```bash
./dev-stop.sh
```

### Available Services
- **Frontend**: http://localhost:4200 (Angular with hot reload)
- **Backend API**: http://localhost:8081
- **Exchange Rate API**: http://localhost:8082
- **Database**: localhost:3307 (MariaDB)
- **MailHog**: http://localhost:8025 (Email testing)
- **J<PERSON><PERSON>**: http://localhost:16686 (Tracing)

### Development vs Production

#### Development Mode (Recommended for daily work)
- ✅ Fast startup (no rebuild needed)
- ✅ Hot reloading for frontend
- ✅ Volume mounts for instant code changes
- ✅ Environment variables from `.env` file
- ✅ Selective service rebuilding

#### Production Mode (For testing production builds)
```bash
cd portal-compose-local
export GITHUB_TOKEN="your_token"
export GITHUB_LOGIN="your_login"  
export OPENEXCHANGERATE_API_KEY="your_key"
docker-compose up --build
```

### Useful Commands

#### View Logs
```bash
cd portal-compose-local
docker-compose logs -f [service-name]
# Examples:
docker-compose logs -f frontend
docker-compose logs -f backend
```

#### Rebuild Specific Service
```bash
./dev-rebuild.sh
# Then select which service to rebuild
```

#### Reset Everything
```bash
./dev-stop.sh
docker-compose down -v  # Remove volumes too
./dev-start.sh
```

### Tips for Efficient Development

1. **Use the development scripts** - They're much faster than full rebuilds
2. **Only rebuild when necessary** - Frontend changes usually don't need rebuilds
3. **Use selective rebuilding** - Only rebuild the service you changed
4. **Keep services running** - Stop/start is faster than rebuild
5. **Monitor logs** - Use `docker-compose logs -f` to debug issues

### Troubleshooting

#### Port Already in Use
```bash
./dev-stop.sh
# Wait a moment, then:
./dev-start.sh
```

#### Database Issues
```bash
cd portal-compose-local
docker-compose down -v  # This removes database data
./dev-start.sh
```

#### Frontend Not Hot Reloading
Check that the volume mount is working:
```bash
docker-compose logs -f frontend
```
