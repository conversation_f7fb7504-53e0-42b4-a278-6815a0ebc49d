import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  matSelectAnimations
} from "./chunk-Z6EMG35P.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Mat<PERSON>refix,
  MatSuffix
} from "./chunk-3JG2SDKY.js";
import "./chunk-FXLTNKCP.js";
import "./chunk-LBPMQ3ZI.js";
import "./chunk-CADGKNJQ.js";
import {
  MatOptgroup,
  MatOption
} from "./chunk-CRJMZU5H.js";
import "./chunk-GBSFTVYH.js";
import "./chunk-WQSBGNFP.js";
import "./chunk-V3ETMAPD.js";
import "./chunk-EAHSXJVS.js";
import "./chunk-WKF3JLTS.js";
import "./chunk-B34AMKSH.js";
import "./chunk-VD4RB4ND.js";
import "./chunk-IRGIHNGC.js";
import "./chunk-SCLXSE37.js";
import "./chunk-JESM3E3O.js";
import "./chunk-WEUX34ES.js";
import "./chunk-WDMUDEB6.js";
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};
//# sourceMappingURL=@angular_material_select.js.map
