import {
  MAT_INPUT_VALUE_ACCESSOR,
  MatIn<PERSON>,
  MatInputModule,
  getMatInputUnsupportedTypeError
} from "./chunk-6US5XJ5V.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>fix,
  MatSuffix
} from "./chunk-DXNWZHIS.js";
import "./chunk-FXLTNKCP.js";
import "./chunk-CADGKNJQ.js";
import "./chunk-6MMO27QF.js";
import "./chunk-PTP556X7.js";
import "./chunk-25CTEM44.js";
import "./chunk-B34AMKSH.js";
import "./chunk-IRGIHNGC.js";
import "./chunk-LBPMQ3ZI.js";
import "./chunk-SCLXSE37.js";
import "./chunk-JESM3E3O.js";
import "./chunk-WEUX34ES.js";
import "./chunk-WDMUDEB6.js";
export {
  MAT_INPUT_VALUE_ACCESSOR,
  MatError,
  MatFormField,
  Mat<PERSON><PERSON>,
  MatInput,
  MatInputModule,
  MatLabel,
  MatPrefix,
  MatSuffix,
  getMatInputUnsupportedTypeError
};
//# sourceMappingURL=@angular_material_input.js.map
