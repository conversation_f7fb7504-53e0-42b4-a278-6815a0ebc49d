import {
  A11yModule,
  ActiveDescendant<PERSON>eyManager,
  AriaDescriber,
  CDK_DESCRIBEDBY_HOST_ATTRIBUTE,
  CDK_DESCRIBEDBY_ID_PREFIX,
  CdkAriaLive,
  CdkMonitorFocus,
  CdkTrapFocus,
  ConfigurableFocusTrap,
  ConfigurableFocusTrapFactory,
  EventListenerFocusTrapInertStrategy,
  FOCUS_MONITOR_DEFAULT_OPTIONS,
  FOCUS_TRAP_INERT_STRATEGY,
  FocusKeyManager,
  FocusMonitor,
  FocusMonitorDetectionMode,
  FocusTrap,
  FocusTrapFactory,
  HighContrastMode,
  HighContrastModeDetector,
  INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,
  INPUT_MODALITY_DETECTOR_OPTIONS,
  InputModalityDetector,
  InteractivityChecker,
  IsFocusableConfig,
  LIVE_ANNOUNCER_DEFAULT_OPTIONS,
  LIVE_ANNOUNCER_ELEMENT_TOKEN,
  LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,
  ListKeyManager,
  LiveAnnouncer,
  MESSAGES_CONTAINER_ID,
  addAriaReferencedId,
  getAriaReferenceIds,
  isFakeMousedownFromScreenReader,
  isFakeTouchstartFromScreenReader,
  removeAriaReferencedId
} from "./chunk-PTP556X7.js";
import "./chunk-25CTEM44.js";
import "./chunk-B34AMKSH.js";
import "./chunk-SCLXSE37.js";
import "./chunk-JESM3E3O.js";
import "./chunk-WEUX34ES.js";
import "./chunk-WDMUDEB6.js";
export {
  A11yModule,
  ActiveDescendantKeyManager,
  AriaDescriber,
  CDK_DESCRIBEDBY_HOST_ATTRIBUTE,
  CDK_DESCRIBEDBY_ID_PREFIX,
  CdkAriaLive,
  CdkMonitorFocus,
  CdkTrapFocus,
  ConfigurableFocusTrap,
  ConfigurableFocusTrapFactory,
  EventListenerFocusTrapInertStrategy,
  FOCUS_MONITOR_DEFAULT_OPTIONS,
  FOCUS_TRAP_INERT_STRATEGY,
  FocusKeyManager,
  FocusMonitor,
  FocusMonitorDetectionMode,
  FocusTrap,
  FocusTrapFactory,
  HighContrastMode,
  HighContrastModeDetector,
  INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,
  INPUT_MODALITY_DETECTOR_OPTIONS,
  InputModalityDetector,
  InteractivityChecker,
  IsFocusableConfig,
  LIVE_ANNOUNCER_DEFAULT_OPTIONS,
  LIVE_ANNOUNCER_ELEMENT_TOKEN,
  LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,
  ListKeyManager,
  LiveAnnouncer,
  MESSAGES_CONTAINER_ID,
  addAriaReferencedId,
  getAriaReferenceIds,
  isFakeMousedownFromScreenReader,
  isFakeTouchstartFromScreenReader,
  removeAriaReferencedId
};
//# sourceMappingURL=@angular_cdk_a11y.js.map
