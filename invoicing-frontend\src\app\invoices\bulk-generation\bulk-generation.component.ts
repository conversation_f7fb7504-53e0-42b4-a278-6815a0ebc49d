import { Component, Inject, LOCALE_ID } from "@angular/core";
import { ActionDef, ColumnDef, ColumnType } from "../../models/table-defs";
import { FilterableTableComponent } from "../../widgets/filterable-table/filterable-table.component";
import { FormControl, FormsModule } from "@angular/forms";
import { BulkInvoiceComponent } from "../../../widgets/bulk-invoice/bulk-invoice.component";
import { ClientLongName } from "../../models/client";
import { MatterService } from "../../services/matter.service";
import { DisplayMatter, Matter } from "../../models/matter";
import { UserService } from "../../services/user.service";
import { LayoutComponent } from "../../layout/layout/layout.component";
import { ListResults } from "../../models/list-results";
import { User } from "../../models/user";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { provideNativeDateAdapter } from "@angular/material/core";
import { MatInputModule } from "@angular/material/input";
import { formatDate } from "@angular/common";

@Component({
  selector: "app-bulk-generation",
  standalone: true,
  imports: [
    BulkInvoiceComponent,
    FilterableTableComponent,
    FormsModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
  ],
  templateUrl: "./bulk-generation.component.html",
  styleUrl: "./bulk-generation.component.scss",
  providers: [provideNativeDateAdapter()],
})
export class BulkGenerationComponent {
  matters: Matter[] = [];
  endDate?: Date;
  startDate?: Date;
  public clientColumnDefs: ColumnDef[] = [
    {
      field: "client",
      name: "global.client",
      filterable: true,
      type: ColumnType.text,
      filterValue: new FormControl(""),
      filterField: "clientLongName",
      multiFieldFormatter: (value: any) => {
        return ClientLongName(value.client);
      },
    },
    {
      field: "applicationTitle",
      name: "global.matter",
      filterable: true,
      type: ColumnType.text,
      filterValue: new FormControl(""),
      filterField: "name",
      multiFieldFormatter: (value: any) => {
        return DisplayMatter(value);
      },
    },
    {
      field: "client.biller",
      name: "Biller",
      filterable: true,
      type: ColumnType.select,
      filterField: "billerIds",
      filterValue: new FormControl(""),
      filterValues: [],
      formatter: (value: any) => {
        return value ? value.lastName + ", " + value.firstName : "";
      },
    },
    {
      name: "invoices.entryCount",
      field: "entryCount",
      filterable: false,
      type: ColumnType.number,
    },
  ];

  public bulkActions: ActionDef[] = [];
  public clientTableActions: ActionDef[] = [];
  public selectedMatters: number[] = [];

  public filters = { hasEntries: true, entryEndDate: "", entryStartDate: "" };

  constructor(
    public matterService: MatterService,
    private userService: UserService,
    @Inject(LayoutComponent) private parent: LayoutComponent,
    @Inject(LOCALE_ID) private locale: string
  ) {
    this.parent.title = "Generate dddd";
  }

  ngOnInit() {
    this.userService.getAll<User>({ isAdmin: true, minimal: true }).subscribe((data: ListResults<User>) => {
      this.clientColumnDefs[2]?.filterValues?.push({ value: "" });
      data.results.forEach((user: any) => {
        this.clientColumnDefs[2]?.filterValues?.push({
          value: user.id,
          label: user,
        });
      });
    });
  }

  updateList($event: any) {
    this.matters = $event;
  }

  filter($event: any) {
    console.dir(this);
    if ($event == null) {
      this.filters = { hasEntries: true, entryEndDate: "", entryStartDate: "" };
      return;
    }
    this.filters = {
      hasEntries: true,
      entryStartDate: this.startDate ? formatDate(this.startDate, "yyyy-MM-dd", this.locale) : "",
      entryEndDate: this.endDate ? formatDate(this.endDate, "yyyy-MM-dd", this.locale) : "",
    };
  }
}
