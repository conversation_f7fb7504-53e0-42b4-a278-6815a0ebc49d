@echo off
echo 🔧 Setting up MatterhornMVP for local development...

echo 📦 Installing Frontend dependencies...
cd invoicing-frontend
call npm install
cd ..

echo 🔧 Installing Backend dependencies...
cd backend-api
go mod tidy
go mod vendor
cd ..

echo 💱 Installing Exchange Rate API dependencies...
cd exchange-rates-api
go mod tidy
go mod vendor
cd ..

echo ✅ Setup complete!
echo 💡 Now run: start-local.bat to start development

pause
