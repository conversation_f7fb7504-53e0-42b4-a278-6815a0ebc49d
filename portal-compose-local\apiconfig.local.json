{"base": {"enableTLS": false, "port": 8081, "logging": {"logLevel": "debug", "logFormat": "text", "enableRequestLogging": true}, "telemetry": {"metrics": {"enable": true}, "tracing": {"enable": true, "url": "http://localhost:14268/api/traces", "type": "j<PERSON><PERSON>"}}, "responseCache": {"enable": false}, "enableGracefulShutdown": false}, "dbs": {"sqlite": {"file": "dev.db"}}, "auth": {"jwtSecretKey": "it's a secret"}, "smtp": {"host": "localhost", "port": 1025, "email": "<EMAIL>"}}