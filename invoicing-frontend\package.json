{"name": "invoicing-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/cdk": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/google-maps": "^19.0.0", "@angular/material": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@auth0/angular-jwt": "^5.2.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "material-design-icons": "^3.0.1", "rxjs": "~7.8.0", "tslib": "^2.8.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.0", "@angular/cli": "^19.0.0", "@angular/compiler-cli": "^19.0.0", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.20", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.41", "prettier": "3.3.3", "tailwindcss": "^3.4.9", "typescript": "~5.6.0"}}