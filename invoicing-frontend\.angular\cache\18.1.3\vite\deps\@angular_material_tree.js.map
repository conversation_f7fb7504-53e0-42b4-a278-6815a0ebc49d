{"version": 3, "sources": ["../../../../../node_modules/@angular/material/fesm2022/tree.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/tree';\nimport { CdkTreeNode, CdkTreeNodeDef, CdkNestedTreeNode, CDK_TREE_NODE_OUTLET_NODE, CdkTreeNodePadding, CdkTreeNodeOutlet, CdkTree, CdkTreeNodeToggle, CdkTreeModule } from '@angular/cdk/tree';\nimport * as i0 from '@angular/core';\nimport { booleanAttribute, numberAttribute, Directive, Attribute, Input, Inject, Optional, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport { DataSource } from '@angular/cdk/collections';\nimport { BehaviorSubject, merge } from 'rxjs';\nimport { take, map } from 'rxjs/operators';\n\n/**\n * Wrapper for the CdkTree node with Material design styles.\n */\nclass MatTreeNode extends CdkTreeNode {\n  constructor(elementRef, tree, tabIndex) {\n    super(elementRef, tree);\n    /** Whether the node is disabled. */\n    this.disabled = false;\n    this.tabIndex = Number(tabIndex) || 0;\n  }\n  // This is a workaround for https://github.com/angular/angular/issues/23091\n  // In aot mode, the lifecycle hooks from parent class are not called.\n  ngOnInit() {\n    super.ngOnInit();\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n  }\n  static {\n    this.ɵfac = function MatTreeNode_Factory(ɵt) {\n      return new (ɵt || MatTreeNode)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.CdkTree), i0.ɵɵinjectAttribute('tabindex'));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTreeNode,\n      selectors: [[\"mat-tree-node\"]],\n      hostAttrs: [1, \"mat-tree-node\"],\n      inputs: {\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)]\n      },\n      exportAs: [\"matTreeNode\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTreeNode,\n        useExisting: MatTreeNode\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-tree-node',\n      exportAs: 'matTreeNode',\n      providers: [{\n        provide: CdkTreeNode,\n        useExisting: MatTreeNode\n      }],\n      host: {\n        'class': 'mat-tree-node'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1.CdkTree\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }], {\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }]\n  });\n})();\n/**\n * Wrapper for the CdkTree node definition with Material design styles.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\nclass MatTreeNodeDef extends CdkTreeNodeDef {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatTreeNodeDef_BaseFactory;\n      return function MatTreeNodeDef_Factory(ɵt) {\n        return (ɵMatTreeNodeDef_BaseFactory || (ɵMatTreeNodeDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatTreeNodeDef)))(ɵt || MatTreeNodeDef);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTreeNodeDef,\n      selectors: [[\"\", \"matTreeNodeDef\", \"\"]],\n      inputs: {\n        when: [0, \"matTreeNodeDefWhen\", \"when\"],\n        data: [0, \"matTreeNode\", \"data\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTreeNodeDef,\n        useExisting: MatTreeNodeDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNodeDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matTreeNodeDef]',\n      inputs: [{\n        name: 'when',\n        alias: 'matTreeNodeDefWhen'\n      }],\n      providers: [{\n        provide: CdkTreeNodeDef,\n        useExisting: MatTreeNodeDef\n      }],\n      standalone: true\n    }]\n  }], null, {\n    data: [{\n      type: Input,\n      args: ['matTreeNode']\n    }]\n  });\n})();\n/**\n * Wrapper for the CdkTree nested node with Material design styles.\n */\nclass MatNestedTreeNode extends CdkNestedTreeNode {\n  /** Tabindex for the node. */\n  get tabIndex() {\n    return this.disabled ? -1 : this._tabIndex;\n  }\n  set tabIndex(value) {\n    // If the specified tabIndex value is null or undefined, fall back to the default value.\n    this._tabIndex = value != null ? value : 0;\n  }\n  constructor(elementRef, tree, differs, tabIndex) {\n    super(elementRef, tree, differs);\n    /** Whether the node is disabled. */\n    this.disabled = false;\n    this.tabIndex = Number(tabIndex) || 0;\n  }\n  // This is a workaround for https://github.com/angular/angular/issues/19145\n  // In aot mode, the lifecycle hooks from parent class are not called.\n  // TODO(tinayuangao): Remove when the angular issue #19145 is fixed\n  ngOnInit() {\n    super.ngOnInit();\n  }\n  ngAfterContentInit() {\n    super.ngAfterContentInit();\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n  }\n  static {\n    this.ɵfac = function MatNestedTreeNode_Factory(ɵt) {\n      return new (ɵt || MatNestedTreeNode)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.CdkTree), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵinjectAttribute('tabindex'));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatNestedTreeNode,\n      selectors: [[\"mat-nested-tree-node\"]],\n      hostAttrs: [1, \"mat-nested-tree-node\"],\n      inputs: {\n        node: [0, \"matNestedTreeNode\", \"node\"],\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        tabIndex: \"tabIndex\"\n      },\n      exportAs: [\"matNestedTreeNode\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkNestedTreeNode,\n        useExisting: MatNestedTreeNode\n      }, {\n        provide: CdkTreeNode,\n        useExisting: MatNestedTreeNode\n      }, {\n        provide: CDK_TREE_NODE_OUTLET_NODE,\n        useExisting: MatNestedTreeNode\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNestedTreeNode, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-nested-tree-node',\n      exportAs: 'matNestedTreeNode',\n      providers: [{\n        provide: CdkNestedTreeNode,\n        useExisting: MatNestedTreeNode\n      }, {\n        provide: CdkTreeNode,\n        useExisting: MatNestedTreeNode\n      }, {\n        provide: CDK_TREE_NODE_OUTLET_NODE,\n        useExisting: MatNestedTreeNode\n      }],\n      host: {\n        'class': 'mat-nested-tree-node'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1.CdkTree\n  }, {\n    type: i0.IterableDiffers\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }], {\n    node: [{\n      type: Input,\n      args: ['matNestedTreeNode']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Wrapper for the CdkTree padding with Material design styles.\n */\nclass MatTreeNodePadding extends CdkTreeNodePadding {\n  /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n  get level() {\n    return this._level;\n  }\n  set level(value) {\n    this._setLevelInput(value);\n  }\n  /** The indent for each level. Default number 40px from material design menu sub-menu spec. */\n  get indent() {\n    return this._indent;\n  }\n  set indent(indent) {\n    this._setIndentInput(indent);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatTreeNodePadding_BaseFactory;\n      return function MatTreeNodePadding_Factory(ɵt) {\n        return (ɵMatTreeNodePadding_BaseFactory || (ɵMatTreeNodePadding_BaseFactory = i0.ɵɵgetInheritedFactory(MatTreeNodePadding)))(ɵt || MatTreeNodePadding);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTreeNodePadding,\n      selectors: [[\"\", \"matTreeNodePadding\", \"\"]],\n      inputs: {\n        level: [2, \"matTreeNodePadding\", \"level\", numberAttribute],\n        indent: [0, \"matTreeNodePaddingIndent\", \"indent\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTreeNodePadding,\n        useExisting: MatTreeNodePadding\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNodePadding, [{\n    type: Directive,\n    args: [{\n      selector: '[matTreeNodePadding]',\n      providers: [{\n        provide: CdkTreeNodePadding,\n        useExisting: MatTreeNodePadding\n      }],\n      standalone: true\n    }]\n  }], null, {\n    level: [{\n      type: Input,\n      args: [{\n        alias: 'matTreeNodePadding',\n        transform: numberAttribute\n      }]\n    }],\n    indent: [{\n      type: Input,\n      args: ['matTreeNodePaddingIndent']\n    }]\n  });\n})();\n\n/**\n * Outlet for nested CdkNode. Put `[matTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\nclass MatTreeNodeOutlet {\n  constructor(viewContainer, _node) {\n    this.viewContainer = viewContainer;\n    this._node = _node;\n  }\n  static {\n    this.ɵfac = function MatTreeNodeOutlet_Factory(ɵt) {\n      return new (ɵt || MatTreeNodeOutlet)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_TREE_NODE_OUTLET_NODE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTreeNodeOutlet,\n      selectors: [[\"\", \"matTreeNodeOutlet\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTreeNodeOutlet,\n        useExisting: MatTreeNodeOutlet\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNodeOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[matTreeNodeOutlet]',\n      providers: [{\n        provide: CdkTreeNodeOutlet,\n        useExisting: MatTreeNodeOutlet\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_TREE_NODE_OUTLET_NODE]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\n\n/**\n * Wrapper for the CdkTable with Material design styles.\n */\nclass MatTree extends CdkTree {\n  constructor() {\n    super(...arguments);\n    // Outlets within the tree's template where the dataNodes will be inserted.\n    // We need an initializer here to avoid a TS error. The value will be set in `ngAfterViewInit`.\n    this._nodeOutlet = undefined;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatTree_BaseFactory;\n      return function MatTree_Factory(ɵt) {\n        return (ɵMatTree_BaseFactory || (ɵMatTree_BaseFactory = i0.ɵɵgetInheritedFactory(MatTree)))(ɵt || MatTree);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTree,\n      selectors: [[\"mat-tree\"]],\n      viewQuery: function MatTree_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatTreeNodeOutlet, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._nodeOutlet = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"tree\", 1, \"mat-tree\"],\n      exportAs: [\"matTree\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTree,\n        useExisting: MatTree\n      }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"matTreeNodeOutlet\", \"\"]],\n      template: function MatTree_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [MatTreeNodeOutlet],\n      styles: [\".mat-tree{display:block;background-color:var(--mat-tree-container-background-color)}.mat-tree-node,.mat-nested-tree-node{color:var(--mat-tree-node-text-color);font-family:var(--mat-tree-node-text-font);font-size:var(--mat-tree-node-text-size);font-weight:var(--mat-tree-node-text-weight)}.mat-tree-node{display:flex;align-items:center;flex:1;word-wrap:break-word;min-height:var(--mat-tree-node-min-height)}.mat-nested-tree-node{border-bottom-width:0}\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTree, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tree',\n      exportAs: 'matTree',\n      template: `<ng-container matTreeNodeOutlet></ng-container>`,\n      host: {\n        'class': 'mat-tree',\n        'role': 'tree'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [{\n        provide: CdkTree,\n        useExisting: MatTree\n      }],\n      standalone: true,\n      imports: [MatTreeNodeOutlet],\n      styles: [\".mat-tree{display:block;background-color:var(--mat-tree-container-background-color)}.mat-tree-node,.mat-nested-tree-node{color:var(--mat-tree-node-text-color);font-family:var(--mat-tree-node-text-font);font-size:var(--mat-tree-node-text-size);font-weight:var(--mat-tree-node-text-weight)}.mat-tree-node{display:flex;align-items:center;flex:1;word-wrap:break-word;min-height:var(--mat-tree-node-min-height)}.mat-nested-tree-node{border-bottom-width:0}\"]\n    }]\n  }], null, {\n    _nodeOutlet: [{\n      type: ViewChild,\n      args: [MatTreeNodeOutlet, {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Wrapper for the CdkTree's toggle with Material design styles.\n */\nclass MatTreeNodeToggle extends CdkTreeNodeToggle {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatTreeNodeToggle_BaseFactory;\n      return function MatTreeNodeToggle_Factory(ɵt) {\n        return (ɵMatTreeNodeToggle_BaseFactory || (ɵMatTreeNodeToggle_BaseFactory = i0.ɵɵgetInheritedFactory(MatTreeNodeToggle)))(ɵt || MatTreeNodeToggle);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatTreeNodeToggle,\n      selectors: [[\"\", \"matTreeNodeToggle\", \"\"]],\n      inputs: {\n        recursive: [0, \"matTreeNodeToggleRecursive\", \"recursive\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTreeNodeToggle,\n        useExisting: MatTreeNodeToggle\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeNodeToggle, [{\n    type: Directive,\n    args: [{\n      selector: '[matTreeNodeToggle]',\n      providers: [{\n        provide: CdkTreeNodeToggle,\n        useExisting: MatTreeNodeToggle\n      }],\n      inputs: [{\n        name: 'recursive',\n        alias: 'matTreeNodeToggleRecursive'\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\nconst MAT_TREE_DIRECTIVES = [MatNestedTreeNode, MatTreeNodeDef, MatTreeNodePadding, MatTreeNodeToggle, MatTree, MatTreeNode, MatTreeNodeOutlet];\nclass MatTreeModule {\n  static {\n    this.ɵfac = function MatTreeModule_Factory(ɵt) {\n      return new (ɵt || MatTreeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatTreeModule,\n      imports: [CdkTreeModule, MatCommonModule, MatNestedTreeNode, MatTreeNodeDef, MatTreeNodePadding, MatTreeNodeToggle, MatTree, MatTreeNode, MatTreeNodeOutlet],\n      exports: [MatCommonModule, MatNestedTreeNode, MatTreeNodeDef, MatTreeNodePadding, MatTreeNodeToggle, MatTree, MatTreeNode, MatTreeNodeOutlet]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CdkTreeModule, MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTreeModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkTreeModule, MatCommonModule, ...MAT_TREE_DIRECTIVES],\n      exports: [MatCommonModule, MAT_TREE_DIRECTIVES]\n    }]\n  }], null, null);\n})();\n\n/**\n * Tree flattener to convert a normal type of node to node with children & level information.\n * Transform nested nodes of type `T` to flattened nodes of type `F`.\n *\n * For example, the input data of type `T` is nested, and contains its children data:\n *   SomeNode: {\n *     key: 'Fruits',\n *     children: [\n *       NodeOne: {\n *         key: 'Apple',\n *       },\n *       NodeTwo: {\n *        key: 'Pear',\n *      }\n *    ]\n *  }\n *  After flattener flatten the tree, the structure will become\n *  SomeNode: {\n *    key: 'Fruits',\n *    expandable: true,\n *    level: 1\n *  },\n *  NodeOne: {\n *    key: 'Apple',\n *    expandable: false,\n *    level: 2\n *  },\n *  NodeTwo: {\n *   key: 'Pear',\n *   expandable: false,\n *   level: 2\n * }\n * and the output flattened type is `F` with additional information.\n */\nclass MatTreeFlattener {\n  constructor(transformFunction, getLevel, isExpandable, getChildren) {\n    this.transformFunction = transformFunction;\n    this.getLevel = getLevel;\n    this.isExpandable = isExpandable;\n    this.getChildren = getChildren;\n  }\n  _flattenNode(node, level, resultNodes, parentMap) {\n    const flatNode = this.transformFunction(node, level);\n    resultNodes.push(flatNode);\n    if (this.isExpandable(flatNode)) {\n      const childrenNodes = this.getChildren(node);\n      if (childrenNodes) {\n        if (Array.isArray(childrenNodes)) {\n          this._flattenChildren(childrenNodes, level, resultNodes, parentMap);\n        } else {\n          childrenNodes.pipe(take(1)).subscribe(children => {\n            this._flattenChildren(children, level, resultNodes, parentMap);\n          });\n        }\n      }\n    }\n    return resultNodes;\n  }\n  _flattenChildren(children, level, resultNodes, parentMap) {\n    children.forEach((child, index) => {\n      let childParentMap = parentMap.slice();\n      childParentMap.push(index != children.length - 1);\n      this._flattenNode(child, level + 1, resultNodes, childParentMap);\n    });\n  }\n  /**\n   * Flatten a list of node type T to flattened version of node F.\n   * Please note that type T may be nested, and the length of `structuredData` may be different\n   * from that of returned list `F[]`.\n   */\n  flattenNodes(structuredData) {\n    let resultNodes = [];\n    structuredData.forEach(node => this._flattenNode(node, 0, resultNodes, []));\n    return resultNodes;\n  }\n  /**\n   * Expand flattened node with current expansion status.\n   * The returned list may have different length.\n   */\n  expandFlattenedNodes(nodes, treeControl) {\n    let results = [];\n    let currentExpand = [];\n    currentExpand[0] = true;\n    nodes.forEach(node => {\n      let expand = true;\n      for (let i = 0; i <= this.getLevel(node); i++) {\n        expand = expand && currentExpand[i];\n      }\n      if (expand) {\n        results.push(node);\n      }\n      if (this.isExpandable(node)) {\n        currentExpand[this.getLevel(node) + 1] = treeControl.isExpanded(node);\n      }\n    });\n    return results;\n  }\n}\n/**\n * Data source for flat tree.\n * The data source need to handle expansion/collapsion of the tree node and change the data feed\n * to `MatTree`.\n * The nested tree nodes of type `T` are flattened through `MatTreeFlattener`, and converted\n * to type `F` for `MatTree` to consume.\n */\nclass MatTreeFlatDataSource extends DataSource {\n  get data() {\n    return this._data.value;\n  }\n  set data(value) {\n    this._data.next(value);\n    this._flattenedData.next(this._treeFlattener.flattenNodes(this.data));\n    this._treeControl.dataNodes = this._flattenedData.value;\n  }\n  constructor(_treeControl, _treeFlattener, initialData) {\n    super();\n    this._treeControl = _treeControl;\n    this._treeFlattener = _treeFlattener;\n    this._flattenedData = new BehaviorSubject([]);\n    this._expandedData = new BehaviorSubject([]);\n    this._data = new BehaviorSubject([]);\n    if (initialData) {\n      // Assign the data through the constructor to ensure that all of the logic is executed.\n      this.data = initialData;\n    }\n  }\n  connect(collectionViewer) {\n    return merge(collectionViewer.viewChange, this._treeControl.expansionModel.changed, this._flattenedData).pipe(map(() => {\n      this._expandedData.next(this._treeFlattener.expandFlattenedNodes(this._flattenedData.value, this._treeControl));\n      return this._expandedData.value;\n    }));\n  }\n  disconnect() {\n    // no op\n  }\n}\n\n/**\n * Data source for nested tree.\n *\n * The data source for nested tree doesn't have to consider node flattener, or the way to expand\n * or collapse. The expansion/collapsion will be handled by TreeControl and each non-leaf node.\n */\nclass MatTreeNestedDataSource extends DataSource {\n  constructor() {\n    super(...arguments);\n    this._data = new BehaviorSubject([]);\n  }\n  /**\n   * Data for the nested tree\n   */\n  get data() {\n    return this._data.value;\n  }\n  set data(value) {\n    this._data.next(value);\n  }\n  connect(collectionViewer) {\n    return merge(...[collectionViewer.viewChange, this._data]).pipe(map(() => this.data));\n  }\n  disconnect() {\n    // no op\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatNestedTreeNode, MatTree, MatTreeFlatDataSource, MatTreeFlattener, MatTreeModule, MatTreeNestedDataSource, MatTreeNode, MatTreeNodeDef, MatTreeNodeOutlet, MatTreeNodePadding, MatTreeNodeToggle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,eAAN,MAAM,qBAAoB,YAAY;AAAA,EACpC,YAAY,YAAY,MAAM,UAAU;AACtC,UAAM,YAAY,IAAI;AAEtB,SAAK,WAAW;AAChB,SAAK,WAAW,OAAO,QAAQ,KAAK;AAAA,EACtC;AAAA;AAAA;AAAA,EAGA,WAAW;AACT,UAAM,SAAS;AAAA,EACjB;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAAA,EACpB;AAuBF;AArBI,aAAK,OAAO,SAAS,oBAAoB,IAAI;AAC3C,SAAO,KAAK,MAAM,cAAgB,kBAAqB,UAAU,GAAM,kBAAqB,OAAO,GAAM,kBAAkB,UAAU,CAAC;AACxI;AAGA,aAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,EAC7B,WAAW,CAAC,GAAG,eAAe;AAAA,EAC9B,QAAQ;AAAA,IACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACtD,UAAU,CAAC,GAAG,YAAY,YAAY,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK,CAAC;AAAA,EAC3F;AAAA,EACA,UAAU,CAAC,aAAa;AAAA,EACxB,YAAY;AAAA,EACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,IAChC,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC,CAAC,GAAM,0BAA6B,0BAA0B;AACjE,CAAC;AAnCL,IAAM,cAAN;AAAA,CAsCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,kBAAN,MAAM,wBAAuB,eAAe;AAwB5C;AAtBI,gBAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,uBAAuB,IAAI;AACzC,YAAQ,gCAAgC,8BAAiC,sBAAsB,eAAc,IAAI,MAAM,eAAc;AAAA,EACvI;AACF,GAAG;AAGH,gBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,EACtC,QAAQ;AAAA,IACN,MAAM,CAAC,GAAG,sBAAsB,MAAM;AAAA,IACtC,MAAM,CAAC,GAAG,eAAe,MAAM;AAAA,EACjC;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,IAChC,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC,CAAC,GAAM,0BAA0B;AACpC,CAAC;AAtBL,IAAM,iBAAN;AAAA,CAyBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,MACD,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,IAAM,qBAAN,MAAM,2BAA0B,kBAAkB;AAAA;AAAA,EAEhD,IAAI,WAAW;AACb,WAAO,KAAK,WAAW,KAAK,KAAK;AAAA,EACnC;AAAA,EACA,IAAI,SAAS,OAAO;AAElB,SAAK,YAAY,SAAS,OAAO,QAAQ;AAAA,EAC3C;AAAA,EACA,YAAY,YAAY,MAAM,SAAS,UAAU;AAC/C,UAAM,YAAY,MAAM,OAAO;AAE/B,SAAK,WAAW;AAChB,SAAK,WAAW,OAAO,QAAQ,KAAK;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,UAAM,SAAS;AAAA,EACjB;AAAA,EACA,qBAAqB;AACnB,UAAM,mBAAmB;AAAA,EAC3B;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAAA,EACpB;AA8BF;AA5BI,mBAAK,OAAO,SAAS,0BAA0B,IAAI;AACjD,SAAO,KAAK,MAAM,oBAAsB,kBAAqB,UAAU,GAAM,kBAAqB,OAAO,GAAM,kBAAqB,eAAe,GAAM,kBAAkB,UAAU,CAAC;AACxL;AAGA,mBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,EACpC,WAAW,CAAC,GAAG,sBAAsB;AAAA,EACrC,QAAQ;AAAA,IACN,MAAM,CAAC,GAAG,qBAAqB,MAAM;AAAA,IACrC,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACtD,UAAU;AAAA,EACZ;AAAA,EACA,UAAU,CAAC,mBAAmB;AAAA,EAC9B,YAAY;AAAA,EACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,IAChC,SAAS;AAAA,IACT,aAAa;AAAA,EACf,GAAG;AAAA,IACD,SAAS;AAAA,IACT,aAAa;AAAA,EACf,GAAG;AAAA,IACD,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC,CAAC,GAAM,0BAA6B,0BAA0B;AACjE,CAAC;AAtDL,IAAM,oBAAN;AAAA,CAyDC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,sBAAN,MAAM,4BAA2B,mBAAmB;AAAA;AAAA,EAElD,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,eAAe,KAAK;AAAA,EAC3B;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,gBAAgB,MAAM;AAAA,EAC7B;AAwBF;AAtBI,oBAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,2BAA2B,IAAI;AAC7C,YAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,MAAM,mBAAkB;AAAA,EACvJ;AACF,GAAG;AAGH,oBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,EAC1C,QAAQ;AAAA,IACN,OAAO,CAAC,GAAG,sBAAsB,SAAS,eAAe;AAAA,IACzD,QAAQ,CAAC,GAAG,4BAA4B,QAAQ;AAAA,EAClD;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,IAChC,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC,CAAC,GAAM,0BAA6B,0BAA0B;AACjE,CAAC;AApCL,IAAM,qBAAN;AAAA,CAuCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,qBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,eAAe,OAAO;AAChC,SAAK,gBAAgB;AACrB,SAAK,QAAQ;AAAA,EACf;AAiBF;AAfI,mBAAK,OAAO,SAAS,0BAA0B,IAAI;AACjD,SAAO,KAAK,MAAM,oBAAsB,kBAAqB,gBAAgB,GAAM,kBAAkB,2BAA2B,CAAC,CAAC;AACpI;AAGA,mBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,EACzC,YAAY;AAAA,EACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,IAChC,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC,CAAC,CAAC;AACL,CAAC;AAnBL,IAAM,oBAAN;AAAA,CAsBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,WAAN,MAAM,iBAAgB,QAAQ;AAAA,EAC5B,cAAc;AACZ,UAAM,GAAG,SAAS;AAGlB,SAAK,cAAc;AAAA,EACrB;AA0CF;AAxCI,SAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,gBAAgB,IAAI;AAClC,YAAQ,yBAAyB,uBAA0B,sBAAsB,QAAO,IAAI,MAAM,QAAO;AAAA,EAC3G;AACF,GAAG;AAGH,SAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,EACxB,WAAW,SAAS,cAAc,IAAI,KAAK;AACzC,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,mBAAmB,CAAC;AAAA,IACrC;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,IACpE;AAAA,EACF;AAAA,EACA,WAAW,CAAC,QAAQ,QAAQ,GAAG,UAAU;AAAA,EACzC,UAAU,CAAC,SAAS;AAAA,EACpB,YAAY;AAAA,EACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,IAChC,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC,CAAC,GAAM,4BAA+B,mBAAmB;AAAA,EAC1D,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,CAAC;AAAA,EAClC,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,QAAI,KAAK,GAAG;AACV,MAAG,mBAAmB,GAAG,CAAC;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,cAAc,CAAC,iBAAiB;AAAA,EAChC,QAAQ,CAAC,ocAAoc;AAAA,EAC7c,eAAe;AACjB,CAAC;AA9CL,IAAM,UAAN;AAAA,CAiDC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,MACZ,SAAS,CAAC,iBAAiB;AAAA,MAC3B,QAAQ,CAAC,ocAAoc;AAAA,IAC/c,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,qBAAN,MAAM,2BAA0B,kBAAkB;AAuBlD;AArBI,mBAAK,OAAuB,uBAAM;AAChC,MAAI;AACJ,SAAO,SAAS,0BAA0B,IAAI;AAC5C,YAAQ,mCAAmC,iCAAoC,sBAAsB,kBAAiB,IAAI,MAAM,kBAAiB;AAAA,EACnJ;AACF,GAAG;AAGH,mBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,EACzC,QAAQ;AAAA,IACN,WAAW,CAAC,GAAG,8BAA8B,WAAW;AAAA,EAC1D;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,IAChC,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC,CAAC,GAAM,0BAA0B;AACpC,CAAC;AArBL,IAAM,oBAAN;AAAA,CAwBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,QAAQ,CAAC;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AAAA,MACD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAsB,CAAC,mBAAmB,gBAAgB,oBAAoB,mBAAmB,SAAS,aAAa,iBAAiB;AAC9I,IAAM,iBAAN,MAAM,eAAc;AAkBpB;AAhBI,eAAK,OAAO,SAAS,sBAAsB,IAAI;AAC7C,SAAO,KAAK,MAAM,gBAAe;AACnC;AAGA,eAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,SAAS,CAAC,eAAe,iBAAiB,mBAAmB,gBAAgB,oBAAoB,mBAAmB,SAAS,aAAa,iBAAiB;AAAA,EAC3J,SAAS,CAAC,iBAAiB,mBAAmB,gBAAgB,oBAAoB,mBAAmB,SAAS,aAAa,iBAAiB;AAC9I,CAAC;AAGD,eAAK,OAAyB,iBAAiB;AAAA,EAC7C,SAAS,CAAC,eAAe,iBAAiB,eAAe;AAC3D,CAAC;AAhBL,IAAM,gBAAN;AAAA,CAmBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe,iBAAiB,GAAG,mBAAmB;AAAA,MAChE,SAAS,CAAC,iBAAiB,mBAAmB;AAAA,IAChD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAoCH,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,mBAAmB,UAAU,cAAc,aAAa;AAClE,SAAK,oBAAoB;AACzB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,aAAa,MAAM,OAAO,aAAa,WAAW;AAChD,UAAM,WAAW,KAAK,kBAAkB,MAAM,KAAK;AACnD,gBAAY,KAAK,QAAQ;AACzB,QAAI,KAAK,aAAa,QAAQ,GAAG;AAC/B,YAAM,gBAAgB,KAAK,YAAY,IAAI;AAC3C,UAAI,eAAe;AACjB,YAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,eAAK,iBAAiB,eAAe,OAAO,aAAa,SAAS;AAAA,QACpE,OAAO;AACL,wBAAc,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,cAAY;AAChD,iBAAK,iBAAiB,UAAU,OAAO,aAAa,SAAS;AAAA,UAC/D,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,UAAU,OAAO,aAAa,WAAW;AACxD,aAAS,QAAQ,CAAC,OAAO,UAAU;AACjC,UAAI,iBAAiB,UAAU,MAAM;AACrC,qBAAe,KAAK,SAAS,SAAS,SAAS,CAAC;AAChD,WAAK,aAAa,OAAO,QAAQ,GAAG,aAAa,cAAc;AAAA,IACjE,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,gBAAgB;AAC3B,QAAI,cAAc,CAAC;AACnB,mBAAe,QAAQ,UAAQ,KAAK,aAAa,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC;AAC1E,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,OAAO,aAAa;AACvC,QAAI,UAAU,CAAC;AACf,QAAI,gBAAgB,CAAC;AACrB,kBAAc,CAAC,IAAI;AACnB,UAAM,QAAQ,UAAQ;AACpB,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,KAAK,KAAK,SAAS,IAAI,GAAG,KAAK;AAC7C,iBAAS,UAAU,cAAc,CAAC;AAAA,MACpC;AACA,UAAI,QAAQ;AACV,gBAAQ,KAAK,IAAI;AAAA,MACnB;AACA,UAAI,KAAK,aAAa,IAAI,GAAG;AAC3B,sBAAc,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,YAAY,WAAW,IAAI;AAAA,MACtE;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAQA,IAAM,wBAAN,cAAoC,WAAW;AAAA,EAC7C,IAAI,OAAO;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,MAAM,KAAK,KAAK;AACrB,SAAK,eAAe,KAAK,KAAK,eAAe,aAAa,KAAK,IAAI,CAAC;AACpE,SAAK,aAAa,YAAY,KAAK,eAAe;AAAA,EACpD;AAAA,EACA,YAAY,cAAc,gBAAgB,aAAa;AACrD,UAAM;AACN,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB,IAAI,gBAAgB,CAAC,CAAC;AAC5C,SAAK,gBAAgB,IAAI,gBAAgB,CAAC,CAAC;AAC3C,SAAK,QAAQ,IAAI,gBAAgB,CAAC,CAAC;AACnC,QAAI,aAAa;AAEf,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,QAAQ,kBAAkB;AACxB,WAAO,MAAM,iBAAiB,YAAY,KAAK,aAAa,eAAe,SAAS,KAAK,cAAc,EAAE,KAAK,IAAI,MAAM;AACtH,WAAK,cAAc,KAAK,KAAK,eAAe,qBAAqB,KAAK,eAAe,OAAO,KAAK,YAAY,CAAC;AAC9G,aAAO,KAAK,cAAc;AAAA,IAC5B,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,aAAa;AAAA,EAEb;AACF;AAQA,IAAM,0BAAN,cAAsC,WAAW;AAAA,EAC/C,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,QAAQ,IAAI,gBAAgB,CAAC,CAAC;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,MAAM,KAAK,KAAK;AAAA,EACvB;AAAA,EACA,QAAQ,kBAAkB;AACxB,WAAO,MAAM,GAAG,CAAC,iBAAiB,YAAY,KAAK,KAAK,CAAC,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,CAAC;AAAA,EACtF;AAAA,EACA,aAAa;AAAA,EAEb;AACF;", "names": []}