@echo off
echo 🚀 Starting Modern MatterhornMVP Development Environment...

REM Set environment variables
set GITHUB_TOKEN=****************************************
set GITHUB_LOGIN=VaibhavMore64
set OPENEXCHANGERATE_API_KEY=7d15837873bc43c8aa190d1de21de20c

echo 📦 Starting minimal Docker services (only what we need)...
docker-compose -f docker-compose.simple.yml up -d

echo 🔧 Starting Backend API (Local SQLite - No AWS!)...
start "Backend API" cmd /k "cd backend-api && set API_CONF=../local-config.json && set ENV=qa && go run main.go"

echo 💱 Starting Exchange Rate API...
start "Exchange Rate API" cmd /k "cd exchange-rates-api && set API_CONF=../local-config.json && set ENV=qa && set OPENEXCHANGERATE_API_KEY=%OPENEXCHANGERATE_API_KEY% && go run main.go"

echo 🌐 Starting Frontend (Modern Angular)...
start "Frontend" cmd /k "cd invoicing-frontend && npm start"

echo ✅ All services starting!
echo.
echo 🌐 Frontend: http://localhost:4200
echo 🔧 Backend API: http://localhost:8081
echo 💱 Exchange Rate API: http://localhost:8082
echo 📧 MailHog: http://localhost:8025
echo 🔍 Jaeger: http://localhost:16686
echo.
echo 💡 Modern Development Features:
echo   ✅ Frontend hot reload (instant changes)
echo   ✅ Backend uses SQLite (no AWS dependencies)
echo   ✅ Latest Angular version
echo   ✅ Fast startup time
echo   ✅ Easy debugging

pause
