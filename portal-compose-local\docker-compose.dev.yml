version: '3.8'

services:
  # Development overrides for faster development
  frontend:
    volumes:
      - ../invoicing-frontend:/ng-app
      - /ng-app/node_modules  # Prevent node_modules from being overwritten
    environment:
      - NODE_ENV=development
    # Enable hot reloading
    command: npm run start

  backend:
    volumes:
      - ../backend-api:/app
    environment:
      - ENV=dev
      - HOT_RELOAD=true
    # For Go, you might want to use air for hot reloading
    # command: air

  exchange-rate-api:
    volumes:
      - ../exchange-rates-api:/app
    environment:
      - ENV=dev
      - HOT_RELOAD=true
