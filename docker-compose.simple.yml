# Simple setup - only the services you can't run locally
version: '3.8'

services:
  # Only database and supporting services
  db:
    image: mariadb
    restart: always
    ports:
      - "3307:3306"
    environment:
      MARIADB_ROOT_PASSWORD: example
      MARIADB_DATABASE: timetracker2
    volumes:
      - ./portal-compose-local/database:/var/lib/mysql:Z

  # Optional: Keep these if you need them
  jaeger:
    image: jaegertracing/all-in-one:latest
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    ports:
      - "16686:16686"

  mailhog:
    image: mailhog/mailhog
    ports:
      - "1025:1025"
      - "8025:8025"
