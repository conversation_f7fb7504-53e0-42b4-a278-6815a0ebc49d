import {
  AnimationCurves,
  AnimationDurations,
  Date<PERSON>dapter,
  ErrorStateMatcher,
  MATERIAL_SANITY_CHECKS,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatCommonModule,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  MatRipple,
  MatRippleLoader,
  MatRippleModule,
  NativeDateAdapter,
  NativeDateModule,
  RippleRef,
  RippleRenderer,
  RippleState,
  ShowOnDirtyErrorStateMatcher,
  VERSION,
  _ErrorStateTracker,
  _MatI<PERSON><PERSON><PERSON><PERSON><PERSON>ield,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition,
  defaultRippleAnimationConfig,
  mixinColor,
  mixinDisableRipple,
  mixinDisabled,
  mixinErrorState,
  mixinInitialized,
  mixinTabIndex,
  provideNativeDateAdapter,
  setLines
} from "./chunk-6MMO27QF.js";
import "./chunk-PTP556X7.js";
import "./chunk-25CTEM44.js";
import "./chunk-B34AMKSH.js";
import "./chunk-IRGIHNGC.js";
import "./chunk-SCLXSE37.js";
import "./chunk-JESM3E3O.js";
import "./chunk-WEUX34ES.js";
import "./chunk-WDMUDEB6.js";
export {
  AnimationCurves,
  AnimationDurations,
  DateAdapter,
  ErrorStateMatcher,
  MATERIAL_SANITY_CHECKS,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatCommonModule,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  MatRipple,
  MatRippleLoader,
  MatRippleModule,
  NativeDateAdapter,
  NativeDateModule,
  RippleRef,
  RippleRenderer,
  RippleState,
  ShowOnDirtyErrorStateMatcher,
  VERSION,
  _ErrorStateTracker,
  _MatInternalFormField,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition,
  defaultRippleAnimationConfig,
  mixinColor,
  mixinDisableRipple,
  mixinDisabled,
  mixinErrorState,
  mixinInitialized,
  mixinTabIndex,
  provideNativeDateAdapter,
  setLines
};
//# sourceMappingURL=@angular_material_core.js.map
