@echo off
echo 🚀 Modernizing MatterhornMVP for Better Development Experience...

echo 📦 Step 1: Cleaning up old dependencies...
cd invoicing-frontend
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json

echo 📦 Step 2: Installing latest Angular dependencies...
npm install

echo 🔧 Step 3: Updating Angular CLI globally...
npm install -g @angular/cli@latest

echo 📦 Step 4: Installing backend dependencies...
cd ../backend-api
go mod tidy
go mod vendor

echo 💱 Step 5: Installing exchange rate API dependencies...
cd ../exchange-rates-api
go mod tidy
go mod vendor

echo ✅ Modernization complete!
echo.
echo 🎯 Next steps:
echo 1. Start backend services: docker-compose -f docker-compose.simple.yml up -d
echo 2. Start frontend: cd invoicing-frontend && npm start
echo 3. Access at: http://localhost:4200

pause
