{"base": {"enableTLS": false, "port": 8081, "logging": {"logLevel": "debug", "logFormat": "text", "enableRequestLogging": true}, "telemetry": {"metrics": {"enable": false}, "tracing": {"enable": false, "url": "http://localhost:14268/api/traces", "type": "j<PERSON><PERSON>"}}, "responseCache": {"enable": false}, "enableGracefulShutdown": false}, "dbs": {"sqlite": {"file": "local-dev.db"}}, "auth": {"jwtSecretKey": "local-development-secret-key-change-in-production"}, "smtp": {"host": "localhost", "port": 1025, "email": "<EMAIL>"}}