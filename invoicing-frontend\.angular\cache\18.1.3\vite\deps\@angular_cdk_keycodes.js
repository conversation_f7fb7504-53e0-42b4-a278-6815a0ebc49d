import {
  A,
  ALT,
  <PERSON>OSTROPH<PERSON>,
  AT_SIGN,
  B,
  <PERSON><PERSON><PERSON>LAS<PERSON>,
  BACKSPACE,
  C,
  CAPS_LOCK,
  CLOSE_SQUARE_BRACKET,
  COMMA,
  CONTEXT_MENU,
  CONTROL,
  D,
  DASH,
  DELETE,
  DOWN_ARROW,
  E,
  EIGHT,
  END,
  ENTER,
  EQUALS,
  ESCAPE,
  F,
  F1,
  F10,
  F11,
  F12,
  F2,
  F3,
  F4,
  F5,
  F6,
  F7,
  F8,
  F9,
  FF_EQUALS,
  FF_MINUS,
  FF_MUTE,
  FF_SEMICOLON,
  FF_VOLUME_DOWN,
  FF_VOLUME_UP,
  FIRST_MEDIA,
  FIVE,
  FOUR,
  G,
  H,
  HOME,
  I,
  INSERT,
  J,
  K,
  L,
  LAST_MEDIA,
  LEFT_ARROW,
  M,
  MAC_ENTER,
  MAC_META,
  MAC_WK_CMD_LEFT,
  MAC_WK_CMD_RIGHT,
  META,
  MUTE,
  N,
  NINE,
  NUMPAD_DIVIDE,
  NUMPAD_EIGHT,
  NUMPAD_FIVE,
  NUMPAD_FOUR,
  NUMPAD_MINUS,
  NUMPAD_MULTIPLY,
  NUMPAD_NINE,
  NUMPAD_ONE,
  NUMPAD_PERIOD,
  NUMPAD_PLUS,
  NUMPAD_SEVEN,
  NUMPAD_SIX,
  NUMPAD_THREE,
  NUMPAD_TWO,
  NUMPAD_ZERO,
  NUM_CENTER,
  NUM_LOCK,
  O,
  ONE,
  OPEN_SQUARE_BRACKET,
  P,
  PAGE_DOWN,
  PAGE_UP,
  PAUSE,
  PERIOD,
  PLUS_SIGN,
  PRINT_SCREEN,
  Q,
  QUESTION_MARK,
  R,
  RIGHT_ARROW,
  S,
  SCROLL_LOCK,
  SEMICOLON,
  SEVEN,
  SHIFT,
  SINGLE_QUOTE,
  SIX,
  SLASH,
  SPACE,
  T,
  TAB,
  THREE,
  TILDE,
  TWO,
  U,
  UP_ARROW,
  V,
  VOLUME_DOWN,
  VOLUME_UP,
  W,
  X,
  Y,
  Z,
  ZERO,
  hasModifierKey
} from "./chunk-B34AMKSH.js";
import "./chunk-WDMUDEB6.js";
export {
  A,
  ALT,
  APOSTROPHE,
  AT_SIGN,
  B,
  BACKSLASH,
  BACKSPACE,
  C,
  CAPS_LOCK,
  CLOSE_SQUARE_BRACKET,
  COMMA,
  CONTEXT_MENU,
  CONTROL,
  D,
  DASH,
  DELETE,
  DOWN_ARROW,
  E,
  EIGHT,
  END,
  ENTER,
  EQUALS,
  ESCAPE,
  F,
  F1,
  F10,
  F11,
  F12,
  F2,
  F3,
  F4,
  F5,
  F6,
  F7,
  F8,
  F9,
  FF_EQUALS,
  FF_MINUS,
  FF_MUTE,
  FF_SEMICOLON,
  FF_VOLUME_DOWN,
  FF_VOLUME_UP,
  FIRST_MEDIA,
  FIVE,
  FOUR,
  G,
  H,
  HOME,
  I,
  INSERT,
  J,
  K,
  L,
  LAST_MEDIA,
  LEFT_ARROW,
  M,
  MAC_ENTER,
  MAC_META,
  MAC_WK_CMD_LEFT,
  MAC_WK_CMD_RIGHT,
  META,
  MUTE,
  N,
  NINE,
  NUMPAD_DIVIDE,
  NUMPAD_EIGHT,
  NUMPAD_FIVE,
  NUMPAD_FOUR,
  NUMPAD_MINUS,
  NUMPAD_MULTIPLY,
  NUMPAD_NINE,
  NUMPAD_ONE,
  NUMPAD_PERIOD,
  NUMPAD_PLUS,
  NUMPAD_SEVEN,
  NUMPAD_SIX,
  NUMPAD_THREE,
  NUMPAD_TWO,
  NUMPAD_ZERO,
  NUM_CENTER,
  NUM_LOCK,
  O,
  ONE,
  OPEN_SQUARE_BRACKET,
  P,
  PAGE_DOWN,
  PAGE_UP,
  PAUSE,
  PERIOD,
  PLUS_SIGN,
  PRINT_SCREEN,
  Q,
  QUESTION_MARK,
  R,
  RIGHT_ARROW,
  S,
  SCROLL_LOCK,
  SEMICOLON,
  SEVEN,
  SHIFT,
  SINGLE_QUOTE,
  SIX,
  SLASH,
  SPACE,
  T,
  TAB,
  THREE,
  TILDE,
  TWO,
  U,
  UP_ARROW,
  V,
  VOLUME_DOWN,
  VOLUME_UP,
  W,
  X,
  Y,
  Z,
  ZERO,
  hasModifierKey
};
//# sourceMappingURL=@angular_cdk_keycodes.js.map
