@echo off
echo 🚀 Starting MatterhornMVP - Simple Local Development

REM Set environment variables
set GITHUB_TOKEN=****************************************
set GITHUB_LOGIN=VaibhavMore64
set OPENEXCHANGERATE_API_KEY=7d15837873bc43c8aa190d1de21de20c

echo 📦 Starting database and supporting services...
docker-compose -f docker-compose.simple.yml up -d

echo 🔧 Starting Backend API...
start "Backend API" cmd /k "cd backend-api && set API_CONF=../portal-compose-local/apiconfig.json && set ENV=dev && go run main.go"

echo 💱 Starting Exchange Rate API...
start "Exchange Rate API" cmd /k "cd exchange-rates-api && set API_CONF=../portal-compose-local/exchangerateconfig.json && set ENV=dev && set OPENEXCHANGERATE_API_KEY=%OPENEXCHANGERATE_API_KEY% && go run main.go"

echo 🌐 Starting Frontend...
start "Frontend" cmd /k "cd invoicing-frontend && npm start"

echo ✅ All services starting!
echo.
echo 🌐 Frontend: http://localhost:4200
echo 🔧 Backend API: http://localhost:8081
echo 💱 Exchange Rate API: http://localhost:8082
echo 📧 MailHog: http://localhost:8025
echo 🔍 Jaeger: http://localhost:16686
echo 💾 Database: localhost:3307
echo.
echo 💡 Each service runs in its own terminal window
echo 💡 Make changes and they'll reload automatically!
echo 💡 To stop: close the terminal windows and run stop-local.bat

pause
