#!/bin/bash

# Development rebuild script for when you make changes
echo "🔄 Rebuilding services after code changes..."

cd portal-compose-local

# Ask which service to rebuild
echo "Which service do you want to rebuild?"
echo "1) Backend API only"
echo "2) Frontend only" 
echo "3) Exchange Rate API only"
echo "4) All services"
echo "5) Cancel"

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo "🔄 Rebuilding Backend API..."
        docker-compose up -d --build backend
        ;;
    2)
        echo "🔄 Rebuilding Frontend..."
        docker-compose up -d --build frontend
        ;;
    3)
        echo "🔄 Rebuilding Exchange Rate API..."
        docker-compose up -d --build exchange-rate-api
        ;;
    4)
        echo "🔄 Rebuilding all services..."
        docker-compose up -d --build
        ;;
    5)
        echo "❌ Cancelled"
        exit 0
        ;;
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac

echo "✅ Rebuild complete!"
