@echo off
echo 🚀 Starting MatterhornMVP in development mode...

cd portal-compose-local

echo 📦 Starting services in development mode...
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

echo ✅ Services started!
echo 🌐 Frontend: http://localhost:4200
echo 🔧 Backend API: http://localhost:8081
echo 💱 Exchange Rate API: http://localhost:8082
echo 📧 MailHog: http://localhost:8025
echo 🔍 Jaeger: http://localhost:16686
echo.
echo 💡 To view logs: docker-compose logs -f [service-name]
echo 💡 To rebuild after changes: dev-rebuild.bat
echo 💡 To stop: dev-stop.bat

pause
