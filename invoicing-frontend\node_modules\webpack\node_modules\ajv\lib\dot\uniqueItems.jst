{{# def.definitions }}
{{# def.errors }}
{{# def.setupKeyword }}
{{# def.$data }}


{{? ($schema || $isData) && it.opts.uniqueItems !== false }}
  {{? $isData }}
    var {{=$valid}};
    if ({{=$schemaValue}} === false || {{=$schemaValue}} === undefined)
      {{=$valid}} = true;
    else if (typeof {{=$schemaValue}} != 'boolean')
      {{=$valid}} = false;
    else {
  {{?}}

  var i = {{=$data}}.length
    , {{=$valid}} = true
    , j;
  if (i > 1) {
    {{
      var $itemType = it.schema.items && it.schema.items.type
        , $typeIsArray = Array.isArray($itemType);
    }}
    {{? !$itemType || $itemType == 'object' || $itemType == 'array' ||
        ($typeIsArray && ($itemType.indexOf('object') >= 0 || $itemType.indexOf('array') >= 0)) }}
      outer:
      for (;i--;) {
        for (j = i; j--;) {
          if (equal({{=$data}}[i], {{=$data}}[j])) {
            {{=$valid}} = false;
            break outer;
          }
        }
      }
    {{??}}
      var itemIndices = {}, item;
      for (;i--;) {
        var item = {{=$data}}[i];
        {{ var $method = 'checkDataType' + ($typeIsArray ? 's' : ''); }}
        if ({{= it.util[$method]($itemType, 'item', it.opts.strictNumbers, true) }}) continue;
        {{? $typeIsArray}}
          if (typeof item == 'string') item = '"' + item;
        {{?}}
        if (typeof itemIndices[item] == 'number') {
          {{=$valid}} = false;
          j = itemIndices[item];
          break;
        }
        itemIndices[item] = i;
      }
    {{?}}
  }

  {{? $isData }}  }  {{?}}

  if (!{{=$valid}}) {
    {{# def.error:'uniqueItems' }}
  } {{? $breakOnError }} else { {{?}}
{{??}}
  {{? $breakOnError }} if (true) { {{?}}
{{?}}
