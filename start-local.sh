#!/bin/bash

echo "🚀 Starting MatterhornMVP - Simple Local Development"

# Set environment variables
export GITHUB_TOKEN="****************************************"
export GITHUB_LOGIN="VaibhavMore64"
export OPENEXCHANGERATE_API_KEY="7d15837873bc43c8aa190d1de21de20c"

echo "📦 Starting database and supporting services..."
docker-compose -f docker-compose.simple.yml up -d

echo "🔧 Starting Backend API..."
cd backend-api
API_CONF=../portal-compose-local/apiconfig.json ENV=dev go run main.go &
BACKEND_PID=$!
cd ..

echo "💱 Starting Exchange Rate API..."
cd exchange-rates-api
API_CONF=../portal-compose-local/exchangerateconfig.json ENV=dev OPENEXCHANGERATE_API_KEY=$OPENEXCHANGERATE_API_KEY go run main.go &
EXCHANGE_PID=$!
cd ..

echo "🌐 Starting Frontend..."
cd invoicing-frontend
npm start &
FRONTEND_PID=$!
cd ..

echo "✅ All services started!"
echo ""
echo "🌐 Frontend: http://localhost:4200"
echo "🔧 Backend API: http://localhost:8081"
echo "💱 Exchange Rate API: http://localhost:8082"
echo "📧 MailHog: http://localhost:8025"
echo "🔍 Jaeger: http://localhost:16686"
echo "💾 Database: localhost:3307"
echo ""
echo "💡 Press Ctrl+C to stop all services"

# Save PIDs for cleanup
echo $BACKEND_PID > .backend.pid
echo $EXCHANGE_PID > .exchange.pid
echo $FRONTEND_PID > .frontend.pid

# Wait for Ctrl+C
trap 'echo "🛑 Stopping services..."; kill $BACKEND_PID $EXCHANGE_PID $FRONTEND_PID 2>/dev/null; docker-compose -f docker-compose.simple.yml down; rm -f .*.pid; exit' INT

wait
