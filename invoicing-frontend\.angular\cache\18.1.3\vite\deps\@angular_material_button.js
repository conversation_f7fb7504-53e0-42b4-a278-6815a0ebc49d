import {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  Mat<PERSON>nchor,
  MatButton,
  MatButtonModule,
  MatFabAnchor,
  Mat<PERSON><PERSON>Button,
  MatIconAnchor,
  MatIconButton,
  Mat<PERSON>iniFabAnchor,
  MatMiniFabButton
} from "./chunk-7P4YRVF5.js";
import "./chunk-CRJMZU5H.js";
import "./chunk-V3ETMAPD.js";
import "./chunk-WKF3JLTS.js";
import "./chunk-B34AMKSH.js";
import "./chunk-IRGIHNGC.js";
import "./chunk-SCLXSE37.js";
import "./chunk-JESM3E3O.js";
import "./chunk-WEUX34ES.js";
import "./chunk-WDMUDEB6.js";
export {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  MatButtonModule,
  Mat<PERSON>abAnchor,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MatIconAnchor,
  <PERSON><PERSON>con<PERSON>utton,
  MatMiniFabAnchor,
  MatMiniFabButton
};
//# sourceMappingURL=@angular_material_button.js.map
